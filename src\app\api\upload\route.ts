import { NextRequest, NextResponse } from 'next/server';

// 配置为静态导出兼容（Cloudflare Pages使用functions目录）
export const dynamic = 'force-static';

export async function POST(request: NextRequest) {
  try {
    // 读取上传的数据
    const body = await request.arrayBuffer();
    const uploadSize = body.byteLength;
    
    // 模拟处理时间（可选）
    await new Promise(resolve => setTimeout(resolve, 10));
    
    // 返回成功响应
    return NextResponse.json({
      success: true,
      bytesReceived: uploadSize,
      timestamp: Date.now()
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Cache-Control, Pragma',
      }
    });
    
  } catch (error) {
    console.error('Upload API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Cache-Control, Pragma',
      'Access-Control-Max-Age': '86400',
    },
  });
}
