ROSTemplateFormatVersion: '2015-09-01'
Transform: 'Aliyun::Serverless-2018-04-03'
Resources:
  # 北京节点
  ping-service-beijing:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 北京节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-beijing:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs18
        CodeUri: ./
        Description: '北京节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '北京'
          PROVINCE: '北京'
          FC_REGION: 'cn-beijing'
        Events:
          httpTrigger:
            Type: HTTP
            Properties:
              AuthType: ANONYMOUS
              Methods: ['GET', 'POST']

  # 上海节点  
  ping-service-shanghai:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 上海节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-shanghai:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs18
        CodeUri: ./
        Description: '上海节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '上海'
          PROVINCE: '上海'
          FC_REGION: 'cn-shanghai'
        Events:
          httpTrigger:
            Type: HTTP
            Properties:
              AuthType: ANONYMOUS
              Methods: ['GET', 'POST']

  # 深圳节点
  ping-service-shenzhen:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 深圳节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-shenzhen:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs18
        CodeUri: ./
        Description: '深圳节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '深圳'
          PROVINCE: '广东'
          FC_REGION: 'cn-shenzhen'
        Events:
          httpTrigger:
            Type: HTTP
            Properties:
              AuthType: ANONYMOUS
              Methods: ['GET', 'POST']

  # 杭州节点
  ping-service-hangzhou:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 杭州节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-hangzhou:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs18
        CodeUri: ./
        Description: '杭州节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '杭州'
          PROVINCE: '浙江'
          FC_REGION: 'cn-hangzhou'
        Events:
          httpTrigger:
            Type: HTTP
            Properties:
              AuthType: ANONYMOUS
              Methods: ['GET', 'POST']

  # 青岛节点
  ping-service-qingdao:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 青岛节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-qingdao:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs18
        CodeUri: ./
        Description: '青岛节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '青岛'
          PROVINCE: '山东'
          FC_REGION: 'cn-qingdao'
        Events:
          httpTrigger:
            Type: HTTP
            Properties:
              AuthType: ANONYMOUS
              Methods: ['GET', 'POST']
