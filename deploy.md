# 部署指南

## 部署到 Vercel

### 方法一：通过 Vercel CLI

1. 安装 Vercel CLI
```bash
npm i -g vercel
```

2. 登录 Vercel
```bash
vercel login
```

3. 部署项目
```bash
vercel
```

4. 生产部署
```bash
vercel --prod
```

### 方法二：通过 GitHub 集成

1. 将代码推送到 GitHub 仓库
```bash
git add .
git commit -m "Initial commit"
git push origin main
```

2. 在 [Vercel Dashboard](https://vercel.com/dashboard) 中：
   - 点击 "New Project"
   - 选择你的 GitHub 仓库
   - 点击 "Deploy"

### 环境变量配置

在 Vercel 项目设置中添加以下环境变量（如果需要）：
- `NODE_ENV=production`

### 自定义域名

1. 在 Vercel 项目设置中点击 "Domains"
2. 添加你的自定义域名
3. 按照提示配置 DNS 记录

## 部署到其他平台

### Netlify
1. 构建命令：`npm run build`
2. 发布目录：`.next`
3. 需要添加 `netlify.toml` 配置文件

### Railway
1. 连接 GitHub 仓库
2. 自动检测 Next.js 项目
3. 自动部署

### 自托管
1. 构建项目：`npm run build`
2. 启动服务：`npm start`
3. 使用 PM2 或其他进程管理器保持运行

## 性能优化建议

1. 启用 CDN 加速
2. 配置适当的缓存策略
3. 监控应用性能
4. 定期更新依赖包

## 监控和分析

建议集成：
- Google Analytics
- Vercel Analytics
- 错误监控服务（如 Sentry）
