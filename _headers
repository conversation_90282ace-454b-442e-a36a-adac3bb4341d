# Cloudflare Pages Headers 配置

/*
  # 安全头
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  
  # 性能优化
  Cache-Control: public, max-age=31536000, immutable
  
  # CORS 配置（用于测速API）
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type

/api/*
  # API 路由不缓存
  Cache-Control: no-cache, no-store, must-revalidate
  
  # CORS 配置
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization

/_next/static/*
  # 静态资源长期缓存
  Cache-Control: public, max-age=31536000, immutable
