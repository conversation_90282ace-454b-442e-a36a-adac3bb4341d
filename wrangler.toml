# Cloudflare Pages 配置文件
name = "wob-speedtest"
compatibility_date = "2024-01-01"

[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.environment_variables]
NODE_VERSION = "18"
NPM_VERSION = "9"

# Pages 特定配置
pages_build_output_dir = "out"

# 环境变量
[env.production.vars]
CLOUDFLARE_OPTIMIZED = "true"
NODE_ENV = "production"

[env.preview.vars]
CLOUDFLARE_OPTIMIZED = "true"
NODE_ENV = "development"
