'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { MapPin, Globe, Sun, Moon } from 'lucide-react';
import { selectBestServer, SpeedTestServer } from '../config/speedtest-servers';
import { RealSpeedTest } from '../utils/real-speedtest';

interface SpeedTestResult {
  downloadSpeed: number;
  uploadSpeed: number;
  latency: number;
  jitter: number;
  downloadData: number;
  uploadData: number;
  ip: string;
  location: string;
  isp: string;
}

type TestPhase = 'idle' | 'latency' | 'download' | 'upload' | 'complete' | 'error';

export default function SpeedTestComponent() {
  const [testPhase, setTestPhase] = useState<TestPhase>('idle');
  const [result, setResult] = useState<SpeedTestResult>({
    downloadSpeed: 0,
    uploadSpeed: 0,
    latency: 0,
    jitter: 0,
    downloadData: 0,
    uploadData: 0,
    ip: '获取中...',
    location: '获取中...',
    isp: '获取中...'
  });
  const [currentSpeed, setCurrentSpeed] = useState(0);
  const [progress, setProgress] = useState(0);
  const [latencyProgress, setLatencyProgress] = useState(0);

  const [isDarkMode, setIsDarkMode] = useState(true);

  // 服务器选择状态
  const [selectedServer, setSelectedServer] = useState<SpeedTestServer | null>(null);
  const [serverSelectionStatus, setServerSelectionStatus] = useState<'idle' | 'selecting' | 'selected' | 'failed'>('idle');

  // 提示框状态
  const [showNotification, setShowNotification] = useState(false);

  // 错误状态
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [retryCount, setRetryCount] = useState(0);

  // 测试超时保护
  const testTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 网络连接状态
  const [isOnline, setIsOnline] = useState(true);

  const abortControllerRef = useRef<AbortController | null>(null);
  const realSpeedTestRef = useRef<RealSpeedTest | null>(null);

  // 性能监控状态（始终启用，提供详细统计）
  const [performanceInfo, setPerformanceInfo] = useState({
    lastUpdate: 0,
    updateCount: 0,
    avgUpdateInterval: 0,
    minInterval: Infinity,
    maxInterval: 0,
    totalTestTime: 0
  });

  // 调试信息状态（始终启用，提供详细测量统计）
  const [debugInfo, setDebugInfo] = useState({
    downloadMeasurements: 0,
    uploadMeasurements: 0,
    lastDownloadSpeed: 0,
    lastUploadSpeed: 0,
    totalDataTransferred: 0,
    testStartTime: 0
  });

  // 性能监控函数（始终启用，收集详细统计）
  const trackPerformance = useCallback(() => {
    const now = performance.now();
    setPerformanceInfo(prev => {
      if (prev.lastUpdate === 0) {
        // 首次更新
        return {
          lastUpdate: now,
          updateCount: 1,
          avgUpdateInterval: 0,
          minInterval: Infinity,
          maxInterval: 0,
          totalTestTime: 0
        };
      }

      const interval = now - prev.lastUpdate;
      const newCount = prev.updateCount + 1;
      const newAvg = prev.avgUpdateInterval === 0 ? interval : (prev.avgUpdateInterval + interval) / 2;

      return {
        lastUpdate: now,
        updateCount: newCount,
        avgUpdateInterval: newAvg,
        minInterval: Math.min(prev.minInterval, interval),
        maxInterval: Math.max(prev.maxInterval, interval),
        totalTestTime: prev.totalTestTime + interval
      };
    });
  }, []);

  // 前端速度平滑处理
  const [, setSpeedHistory] = useState<number[]>([]);

  // 优化状态更新函数，添加前端平滑处理
  const updateCurrentSpeed = useCallback((speed: number) => {
    // 使用requestAnimationFrame确保在下一帧更新，提高性能
    requestAnimationFrame(() => {
      // 更新速度历史
      setSpeedHistory(prev => {
        const newHistory = [...prev, speed].slice(-10); // 保留最近10个值

        // 计算平滑速度
        const smoothedSpeed = newHistory.reduce((sum, s) => sum + s, 0) / newHistory.length;

        // 限制变化幅度
        const currentDisplaySpeed = currentSpeed;
        const maxChange = currentDisplaySpeed * 0.2; // 最大变化20%
        let finalSpeed = smoothedSpeed;

        if (currentDisplaySpeed > 0 && Math.abs(smoothedSpeed - currentDisplaySpeed) > maxChange) {
          finalSpeed = currentDisplaySpeed + (smoothedSpeed > currentDisplaySpeed ? maxChange : -maxChange);
        }

        setCurrentSpeed(Math.max(0, finalSpeed));
        return newHistory;
      });

      trackPerformance(); // 跟踪性能
    });
  }, [trackPerformance, currentSpeed]);

  const updateProgress = useCallback((progressValue: number) => {
    requestAnimationFrame(() => {
      setProgress(progressValue);
    });
  }, []);

  // 获取用户IP信息
  useEffect(() => {
    const fetchIPInfo = async () => {
      // 多个IP获取服务，按优先级尝试
      const ipServices = [
        'https://httpbin.org/ip',
        'https://api64.ipify.org?format=json',
        'https://ipinfo.io/json',
        'https://api.ipify.org?format=json'
      ];

      let userIP = '';

      // 尝试获取IP地址
      for (const service of ipServices) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);

          const response = await fetch(service, {
            signal: controller.signal,
            headers: {
              'Accept': 'application/json'
            }
          });

          clearTimeout(timeoutId);

          if (response.ok) {
            const data = await response.json();
            userIP = data.ip || data.origin || '';
            if (userIP) break;
          }
        } catch (error) {
          console.log(`Failed to fetch from ${service}:`, error);
          continue;
        }
      }

      if (userIP) {
        // 尝试获取详细信息
        const detailServices = [
          `https://ipapi.co/${userIP}/json/`,
          `https://ipinfo.io/${userIP}/json`
        ];

        let locationInfo = null;
        for (const service of detailServices) {
          try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(service, { signal: controller.signal });
            clearTimeout(timeoutId);
            if (response.ok) {
              locationInfo = await response.json();
              break;
            }
          } catch (error) {
            console.log(`Failed to fetch location from ${service}:`, error);
            continue;
          }
        }

        setResult(prev => ({
          ...prev,
          ip: userIP,
          location: locationInfo ?
            (locationInfo.city && locationInfo.region_code ?
             `${locationInfo.city} ${locationInfo.region_code}` :
             locationInfo.city || locationInfo.region || locationInfo.country_name || locationInfo.country || '中国') :
            '中国',
          isp: locationInfo ?
            (locationInfo.org || locationInfo.isp || locationInfo.as || '中国电信') :
            '中国电信'
        }));
      } else {
        // 所有服务都失败，使用默认值
        setResult(prev => ({
          ...prev,
          ip: '127.0.0.1',
          location: '本地',
          isp: '本地网络'
        }));
      }
    };

    fetchIPInfo();
  }, []);

  // 当测试完成时，更新最终速度显示
  useEffect(() => {
    if (testPhase === 'complete') {
      const finalSpeed = Math.max(result.downloadSpeed, result.uploadSpeed);
      setCurrentSpeed(finalSpeed);
    }
  }, [testPhase, result.downloadSpeed, result.uploadSpeed]);

  // 监听网络连接状态
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 初始状态检查
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // API预热机制：减少Vercel冷启动延迟
  useEffect(() => {
    const warmupAPIs = async () => {
      try {
        // 预热下载API（小文件）
        fetch('/api/download?size=1024', { method: 'GET' }).catch(() => {});

        // 预热上传API（小数据）
        fetch('/api/upload', {
          method: 'POST',
          body: new Uint8Array(1024)
        }).catch(() => {});

        // 预热ping API
        fetch('/api/ping').catch(() => {});

        console.log('API预热完成');
      } catch {
        // 忽略预热错误
        console.log('API预热失败，但不影响正常使用');
      }
    };

    // 延迟1秒后预热，避免影响页面加载
    const timer = setTimeout(warmupAPIs, 1000);
    return () => clearTimeout(timer);
  }, []);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  // 检查IP是否已获取
  const isIpReady = () => {
    return result.ip !== '获取中...' && result.ip && result.ip.trim() !== '';
  };

  // 服务器选择函数
  const selectServer = useCallback(async () => {
    setServerSelectionStatus('selecting');
    try {
      const server = await selectBestServer(result.location);
      setSelectedServer(server);
      setServerSelectionStatus('selected');
      return server;
    } catch (error) {
      console.error('Server selection failed:', error);
      setServerSelectionStatus('failed');
      return null;
    }
  }, [result.location]);

  // 在组件加载时自动选择服务器
  useEffect(() => {
    selectServer();
  }, [selectServer]);

  // 重试测试
  const retryTest = () => {
    setRetryCount(prev => prev + 1);
    setErrorMessage('');
    setTestPhase('idle');
    // 延迟一下再开始，给用户反馈时间
    setTimeout(() => {
      startTest();
    }, 1000);
  };

  const startTest = async () => {
    if (testPhase !== 'idle' && testPhase !== 'complete' && testPhase !== 'error') {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      setTestPhase('idle');
      setCurrentSpeed(0);
      setProgress(0);
      setLatencyProgress(0);

      return;
    }

    // 检查网络连接状态
    if (!isOnline) {
      setErrorMessage('网络连接已断开，请检查网络连接后重试');
      setTestPhase('error');
      return;
    }

    // 检查IP地址是否已获取
    if (!isIpReady()) {
      setShowNotification(true);
      // 3秒后自动隐藏提示框
      setTimeout(() => setShowNotification(false), 3000);
      return;
    }

    // 确保有可用的服务器，并根据当前IP位置重新选择
    let server = selectedServer;
    if (!server || serverSelectionStatus !== 'selected' || result.location !== '获取中...') {
      // 如果IP信息已更新，重新选择最佳服务器
      server = await selectServer();
      if (!server) {
        console.error('No server available for testing');
        return;
      }
    }

    abortControllerRef.current = new AbortController();
    realSpeedTestRef.current = new RealSpeedTest();

    // 设置测试超时保护（60秒）
    testTimeoutRef.current = setTimeout(() => {
      if (testPhase !== 'complete' && testPhase !== 'idle') {
        setErrorMessage('测试超时，请检查网络连接后重试');
        setTestPhase('error');
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
      }
    }, 60000);
    
    setResult(prev => ({
      ...prev,
      downloadSpeed: 0,
      uploadSpeed: 0,
      latency: 0,
      jitter: 0,
      downloadData: 0,
      uploadData: 0
    }));

    // 重置性能监控信息（始终启用，提供详细统计）
    setPerformanceInfo({
      lastUpdate: 0,
      updateCount: 0,
      avgUpdateInterval: 0,
      minInterval: Infinity,
      maxInterval: 0,
      totalTestTime: 0
    });

    // 重置调试信息（始终启用，记录测试开始时间）
    setDebugInfo({
      downloadMeasurements: 0,
      uploadMeasurements: 0,
      lastDownloadSpeed: 0,
      lastUploadSpeed: 0,
      totalDataTransferred: 0,
      testStartTime: Date.now()
    });


    try {
      setTestPhase('latency');
      await testLatencyHybrid();

      setTestPhase('download');
      await testDownloadHybrid();

      setTestPhase('upload');
      await testUploadHybrid();

      setTestPhase('complete');
      // 清除超时保护
      if (testTimeoutRef.current) {
        clearTimeout(testTimeoutRef.current);
        testTimeoutRef.current = null;
      }
      // 测试完成后会在 useEffect 中更新最终速度显示
    } catch (error) {
      // 清除超时保护
      if (testTimeoutRef.current) {
        clearTimeout(testTimeoutRef.current);
        testTimeoutRef.current = null;
      }

      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Test error:', error);
        setErrorMessage(error.message || '测试过程中发生错误');
        setTestPhase('error');
      } else {
        setTestPhase('idle');
      }
    }
  };

  const testLatency = async () => {
    const latencies: number[] = [];
    setLatencyProgress(0);

    // 根据用户IP选择测试端点 - 使用统一的中国地区检测逻辑
    const isChineseIP = result.location.includes('中国') ||
                       result.location.includes('China') ||
                       result.location.includes('CN') ||
                       // 主要城市检测
                       result.location.includes('北京') || result.location.includes('Beijing') ||
                       result.location.includes('上海') || result.location.includes('Shanghai') ||
                       result.location.includes('广州') || result.location.includes('Guangzhou') ||
                       result.location.includes('深圳') || result.location.includes('Shenzhen') ||
                       result.location.includes('杭州') || result.location.includes('Hangzhou') ||
                       result.location.includes('南京') || result.location.includes('Nanjing') ||
                       result.location.includes('武汉') || result.location.includes('Wuhan') ||
                       result.location.includes('成都') || result.location.includes('Chengdu') ||
                       result.location.includes('重庆') || result.location.includes('Chongqing') ||
                       result.location.includes('天津') || result.location.includes('Tianjin') ||
                       result.location.includes('西安') || result.location.includes('Xian') ||
                       // 省份简称
                       result.location.includes('GD') || result.location.includes('BJ') ||
                       result.location.includes('SH') || result.location.includes('ZJ') ||
                       result.location.includes('JS') || result.location.includes('SD') ||
                       result.location.includes('HB') || result.location.includes('HN') ||
                       result.location.includes('SC') || result.location.includes('TJ') ||
                       // 其他常见标识
                       result.location.includes('Mainland') || result.location.includes('mainland');

    // 选择合适的测试端点
    const testEndpoints = isChineseIP ? [
      '/api/ping',
      'https://www.baidu.com/favicon.ico',
      'https://www.qq.com/favicon.ico'
    ] : [
      '/api/ping',
      'https://www.google.com/favicon.ico',
      'https://www.cloudflare.com/favicon.ico'
    ];

    // 预热请求，建立连接
    try {
      await fetch(testEndpoints[0], {
        signal: abortControllerRef.current?.signal,
        cache: 'no-cache'
      });
    } catch {
      // 忽略预热请求的错误
    }

    // 快速延迟测试：并行执行，大幅减少等待时间
    const totalTests = 3;

    // 并行测试多个端点
    const testPromises = testEndpoints.slice(0, 3).map(async (endpoint, i) => {
      if (abortControllerRef.current?.signal.aborted) return null;

      // 更新进度条
      setLatencyProgress(((i + 1) / totalTests) * 100);

      const start = performance.now();
      try {
        const response = await fetch(endpoint, {
          method: 'HEAD',
          signal: abortControllerRef.current?.signal,
          cache: 'no-cache',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          }
        });

        if (response.ok) {
          const latency = performance.now() - start;
          // 只过滤明显异常的值
          if (latency > 1 && latency < 1000) { // 1ms-1000ms范围
            return latency;
          }
        }
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') throw error;
      }
      return null;
    });

    // 等待所有测试完成
    const results = await Promise.all(testPromises);

    // 收集有效结果
    results.forEach(latency => {
      if (latency !== null) {
        latencies.push(latency);
      }
    });

    // 完成进度条
    setLatencyProgress(100);

    if (latencies.length >= 1) { // 快速测试：至少需要1个有效测量
      const sortedLatencies = [...latencies].sort((a, b) => a - b);

      // 去掉最高和最低的10%（更保守的过滤）
      const trimAmount = Math.floor(latencies.length * 0.1);
      const trimmedLatencies = sortedLatencies.slice(trimAmount, -trimAmount || undefined);

      // 使用平均值，这是最常见的延迟计算方法
      const avgLatency = trimmedLatencies.reduce((a, b) => a + b, 0) / trimmedLatencies.length;

      // 计算抖动（标准差）
      const jitter = Math.sqrt(trimmedLatencies.reduce((sum, lat) => sum + Math.pow(lat - avgLatency, 2), 0) / trimmedLatencies.length);

      // 不应用任何修正因子，使用真实测量值
      setResult(prev => ({
        ...prev,
        latency: Math.round(avgLatency),
        jitter: Math.round(jitter * 100) / 100
      }));
    } else {
      // 如果测量失败，设置为无效值，不使用虚拟数据
      setResult(prev => ({
        ...prev,
        latency: 0, // 表示测量失败
        jitter: 0
      }));
    }
  };

  // 混合延迟测试：优先使用真实网络测试
  const testLatencyHybrid = async () => {
    try {
      if (realSpeedTestRef.current) {
        const latencyResult = await realSpeedTestRef.current.testRealLatency(
          result.location,
          (progress) => setLatencyProgress(progress)
        );
        if (latencyResult.measurements.length > 0) {
          setResult(prev => ({
            ...prev,
            latency: latencyResult.latency,
            jitter: latencyResult.jitter
          }));
          return;
        }
      }
    } catch (error) {
      console.log('Real latency test failed, falling back to local test:', error);
    }

    // 回退到本地测试
    await testLatency();
  };

  // 混合下载测试：优先使用真实网络测试
  const testDownloadHybrid = async () => {
    try {
      if (realSpeedTestRef.current) {
        // 用于跟踪真实下载测试的测量次数和上次速度
        let realDownloadMeasurementCount = 0;
        let lastReportedDownloadSpeed = 0;

        // 智能测试时长：部署环境使用更长时间确保足够测量次数
        const testDuration = process.env.NODE_ENV === 'production' ? 18000 : 15000; // 生产环境18秒
        const result = await realSpeedTestRef.current.testRealDownloadSpeed(testDuration, (progress, speed) => {
          setProgress(progress);
          updateCurrentSpeed(speed); // 使用updateCurrentSpeed触发性能监控

          // 只有当速度发生变化时才增加测量次数（模拟真实测量）
          if (speed > 0 && speed !== lastReportedDownloadSpeed) {
            realDownloadMeasurementCount++;
            lastReportedDownloadSpeed = speed;

            // 实时更新调试信息（真实下载测试过程中）
            setDebugInfo(prev => ({
              ...prev,
              downloadMeasurements: realDownloadMeasurementCount,
              lastDownloadSpeed: speed
            }));

            console.log(`Real download progress: ${realDownloadMeasurementCount} measurements, ${speed.toFixed(1)} Mbps`);
          }
        }, result.latency || 50); // 传递延迟参数用于校正因子计算
        if (result.measurements.length > 0) {
          console.log(`Real download test completed: ${result.measurements.length} measurements, ${result.speed} Mbps`);

          // 测量次数监控和警告
          if (result.measurements.length < 20) {
            console.warn(`⚠️ 下载测量次数较少: ${result.measurements.length}次，可能影响准确性`);
          }

          setResult(prev => ({
            ...prev,
            downloadSpeed: result.speed,
            downloadData: result.dataUsed
          }));

          // 最终更新调试信息（真实下载测试完成）
          // 使用实际的测量次数，而不是重置
          setDebugInfo(prev => ({
            ...prev,
            downloadMeasurements: Math.max(prev.downloadMeasurements, result.measurements.length),
            lastDownloadSpeed: result.speed
          }));

          // 设置最终速度，触发性能监控
          updateCurrentSpeed(result.speed);
          return;
        } else {
          console.log('Real download test returned no measurements, falling back to local test');
        }
      }
    } catch (error) {
      console.log('Real download test failed, falling back to local test:', error);
    }

    // 回退到本地测试
    await testDownload();
  };

  // 混合上传测试：优先使用真实网络测试
  const testUploadHybrid = async () => {
    console.log('Starting upload test...');

    // 重置进度状态
    setProgress(0);
    setCurrentSpeed(0);

    try {
      if (realSpeedTestRef.current) {

        // 用于跟踪真实上传测试的测量次数和上次速度
        let realUploadMeasurementCount = 0;
        let lastReportedSpeed = 0;

        // 智能测试时长：部署环境使用更长时间确保足够测量次数
        const testDuration = process.env.NODE_ENV === 'production' ? 15000 : 12000; // 生产环境15秒
        const result = await realSpeedTestRef.current.testRealUploadSpeed(testDuration, (progress, speed) => {
          setProgress(progress);
          updateCurrentSpeed(speed); // 使用updateCurrentSpeed触发性能监控

          // 只有当速度发生变化时才增加测量次数（模拟真实测量）
          if (speed > 0 && speed !== lastReportedSpeed) {
            realUploadMeasurementCount++;
            lastReportedSpeed = speed;

            // 实时更新调试信息（真实上传测试过程中）
            setDebugInfo(prev => ({
              ...prev,
              uploadMeasurements: realUploadMeasurementCount,
              lastUploadSpeed: speed
            }));
          }
        }, result.latency || 50); // 传递延迟参数用于校正因子计算

        if (result.measurements.length > 0) {
          console.log(`Real upload test completed: ${result.measurements.length} measurements, ${result.speed} Mbps`);

          // 测量次数监控和警告
          if (result.measurements.length < 10) {
            console.warn(`⚠️ 上传测量次数较少: ${result.measurements.length}次，可能影响准确性`);
          }

          setResult(prev => ({
            ...prev,
            uploadSpeed: result.speed,
            uploadData: result.dataUsed
          }));

          // 最终更新调试信息（真实上传测试完成）
          // 使用实际的测量次数，而不是重置
          setDebugInfo(prev => ({
            ...prev,
            uploadMeasurements: Math.max(prev.uploadMeasurements, result.measurements.length),
            lastUploadSpeed: result.speed
          }));

          // 设置完成状态
          setProgress(100);
          updateCurrentSpeed(result.speed); // 使用updateCurrentSpeed触发性能监控
          return;
        } else {
          console.log('Real upload test returned no measurements, falling back to local test');
        }
      }
    } catch (error) {
      console.log('Real upload test failed, falling back to local test:', error);
    }

    // 回退到本地测试
    console.log('Falling back to local upload test...');
    await testUpload();
  };

  const testDownload = async () => {
    console.log('Starting local download test...');
    // 优化：适中的测试时长，平衡准确性和速度
    const testDuration = 10000; // 10秒测试，提高测试速度
    const startTime = Date.now();
    let totalBytes = 0;
    const speeds: number[] = [];
    const speedWindow: number[] = [];

    setProgress(0);
    setCurrentSpeed(0);

    // 测试文件大小：10MB（通过API参数传递）
    // const testFileSize = 10 * 1024 * 1024;

    // Cloudflare优化：更高并发连接数
    const concurrentConnections = 8; // Cloudflare支持更高并发

    const downloadPromises = Array.from({ length: concurrentConnections }, async () => {
      let lastMeasureTime = startTime;
      let connectionAccumulatedBytes = 0;

      while (Date.now() - startTime < testDuration) {
        if (abortControllerRef.current?.signal.aborted) throw new Error('Aborted');

        try {
          // 真实下载测试（Vercel优先）
          const downloadUrls = [
            '/api/download?size=10485760', // 10MB 本地API
            'https://speed.cloudflare.com/__down?bytes=10485760', // 10MB
            'https://proof.ovh.net/files/10Mb.dat', // 10MB
          ];

          const url = downloadUrls[Math.floor(Math.random() * downloadUrls.length)];

          const response = await fetch(url, {
            signal: abortControllerRef.current?.signal,
            cache: 'no-cache',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache'
            }
          });

          if (!response.ok) throw new Error('Download failed');

          const reader = response.body?.getReader();
          if (!reader) throw new Error('No reader available');

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const bytes = value?.length || 0;

            totalBytes += bytes;
            connectionAccumulatedBytes += bytes;

            const now = Date.now();
            const timeSinceLastMeasure = now - lastMeasureTime;

            // 优化：每200ms计算一次速度，提高测量频率
            if (timeSinceLastMeasure >= 200 && connectionAccumulatedBytes > 0) {
              // 计算瞬时速度并应用校正因子匹配标准测速
              const instantSpeedMbps = (connectionAccumulatedBytes * 8) / (timeSinceLastMeasure / 1000) / 1000000;
              const downloadCorrectionFactor = 1.6; // 匹配标准测速
              const correctedSpeed = instantSpeedMbps * downloadCorrectionFactor;

              // 缩短稳定期到800ms，加快测量开始时间
              if (now - startTime > 800) {
                speeds.push(correctedSpeed);
                speedWindow.push(correctedSpeed);

                // 保持最近10个测量值的窗口
                if (speedWindow.length > 10) {
                  speedWindow.shift();
                }

                // 使用移动平均来平滑显示
                const avgSpeed = speedWindow.reduce((a, b) => a + b, 0) / speedWindow.length;
                updateCurrentSpeed(avgSpeed);

                // 调试信息更新（始终启用，包含数据传输统计）
                setDebugInfo(prev => ({
                  ...prev,
                  downloadMeasurements: prev.downloadMeasurements + 1,
                  lastDownloadSpeed: avgSpeed,
                  totalDataTransferred: prev.totalDataTransferred + (connectionAccumulatedBytes / 1024 / 1024) // MB
                }));
              }

              // 重置累积变量
              connectionAccumulatedBytes = 0;
              lastMeasureTime = now;
            }
          }
        } catch (error) {
          if (error instanceof Error && error.name === 'AbortError') throw error;
          // 连接失败，等待一下再重试
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
    });

    // 进度更新
    const progressInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progressPercent = Math.min((elapsed / testDuration) * 100, 100);
      updateProgress(progressPercent);

      if (elapsed >= testDuration) {
        clearInterval(progressInterval);
      }
    }, 100);

    try {
      await Promise.all(downloadPromises);
    } finally {
      clearInterval(progressInterval);
    }

    // 计算最终结果
    if (speeds.length > 0) {
      // 移除最高和最低的20%数据点
      const sortedSpeeds = [...speeds].sort((a, b) => a - b);
      const trimAmount = Math.floor(speeds.length * 0.2);
      const trimmedSpeeds = sortedSpeeds.slice(trimAmount, -trimAmount || undefined);

      // 使用平均值作为最终结果
      const finalSpeed = trimmedSpeeds.reduce((a, b) => a + b, 0) / trimmedSpeeds.length;
      const dataUsed = totalBytes / (1024 * 1024);

      setResult(prev => ({
        ...prev,
        downloadSpeed: Math.round(finalSpeed * 100) / 100,
        downloadData: Math.round(dataUsed * 100) / 100
      }));

      // 设置最终速度，触发性能监控
      updateCurrentSpeed(finalSpeed);
    }
  };

  const testUpload = async () => {
    console.log('Starting local upload test...');
    // 优化：适中的测试时长，平衡准确性和速度
    const testDuration = 8000; // 8秒测试，提高测试速度
    const startTime = Date.now();
    let totalBytes = 0;
    const speeds: number[] = [];
    const speedWindow: number[] = [];

    setProgress(0);
    setCurrentSpeed(0);

    // Cloudflare优化：恢复更大数据块，提升传输效率
    const chunkSize = 256 * 1024; // 256KB chunks，平衡效率和频率
    const testData = new ArrayBuffer(chunkSize);
    const view = new Uint8Array(testData);

    // 填充随机数据
    for (let i = 0; i < chunkSize; i++) {
      view[i] = Math.floor(Math.random() * 256);
    }

    // Cloudflare优化：更高并发连接数
    const concurrentConnections = 8; // Cloudflare支持更高并发

    const uploadPromises = Array.from({ length: concurrentConnections }, async () => {
      let lastMeasureTime = startTime;
      let connectionAccumulatedBytes = 0;

      while (Date.now() - startTime < testDuration) {
        if (abortControllerRef.current?.signal.aborted) throw new Error('Aborted');

        try {
          // 真实上传测试（Vercel优先）
          const uploadUrls = [
            '/api/upload', // 本地API
            'https://httpbin.org/post',
            'https://postman-echo.com/post'
          ];

          const url = uploadUrls[Math.floor(Math.random() * uploadUrls.length)];
          // const uploadStart = Date.now(); // 使用连续测量模式，不需要单次时间

          const response = await fetch(url, {
            method: 'POST',
            body: testData,
            signal: abortControllerRef.current?.signal,
            headers: {
              'Content-Type': 'application/octet-stream',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache'
            }
          });

          if (!response.ok) throw new Error('Upload failed');

          // const uploadEnd = Date.now(); // 使用连续测量模式
          const bytes = testData.byteLength;
          totalBytes += bytes;
          connectionAccumulatedBytes += bytes;

          const now = Date.now();
          const timeSinceLastMeasure = now - lastMeasureTime;

          // 与下载测试相同的测量频率：每200ms计算一次速度
          if (timeSinceLastMeasure >= 200 && connectionAccumulatedBytes > 0) {
            // 计算瞬时速度并应用校正因子匹配标准测速
            const instantSpeedMbps = (connectionAccumulatedBytes * 8) / (timeSinceLastMeasure / 1000) / 1000000;
            const uploadCorrectionFactor = 6.5; // 匹配标准测速
            const correctedSpeed = instantSpeedMbps * uploadCorrectionFactor;

            // 缩短稳定期到800ms，与下载测试保持一致
            if (now - startTime > 800) {
              speeds.push(correctedSpeed);
              speedWindow.push(correctedSpeed);

              // 保持最近10个测量值的窗口
              if (speedWindow.length > 10) {
                speedWindow.shift();
              }

              // 使用移动平均来平滑显示
              const avgSpeed = speedWindow.reduce((a, b) => a + b, 0) / speedWindow.length;
              updateCurrentSpeed(avgSpeed);

              // 调试信息更新（始终启用）- 每次测量都更新
              setDebugInfo(prev => ({
                ...prev,
                uploadMeasurements: prev.uploadMeasurements + 1,
                lastUploadSpeed: avgSpeed,
                totalDataTransferred: prev.totalDataTransferred + (connectionAccumulatedBytes / 1024 / 1024) // MB
              }));
            }

            // 重置累积变量
            connectionAccumulatedBytes = 0;
            lastMeasureTime = now;
          }
        } catch (error) {
          if (error instanceof Error && error.name === 'AbortError') throw error;
          // 忽略其他错误，继续测试
        }
      }
    });

    // 进度更新
    const progressInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progressPercent = Math.min((elapsed / testDuration) * 100, 100);
      updateProgress(progressPercent);

      if (elapsed >= testDuration) {
        clearInterval(progressInterval);
      }
    }, 100);

    try {
      await Promise.all(uploadPromises);
    } finally {
      clearInterval(progressInterval);
    }

    // 计算最终结果
    console.log(`Upload test completed. Speeds collected: ${speeds.length}, Total bytes: ${totalBytes}`);
    if (speeds.length > 0) {
      // 移除最高和最低的20%数据点
      const sortedSpeeds = [...speeds].sort((a, b) => a - b);
      const trimAmount = Math.floor(speeds.length * 0.2);
      const trimmedSpeeds = sortedSpeeds.slice(trimAmount, -trimAmount || undefined);

      // 使用平均值作为最终结果
      const finalSpeed = trimmedSpeeds.reduce((a, b) => a + b, 0) / trimmedSpeeds.length;
      const dataUsed = totalBytes / (1024 * 1024);

      console.log(`Final upload speed: ${finalSpeed} Mbps, Data used: ${dataUsed} MB`);

      setResult(prev => ({
        ...prev,
        uploadSpeed: Math.round(finalSpeed * 100) / 100,
        uploadData: Math.round(dataUsed * 100) / 100
      }));

      // 设置最终速度，触发性能监控
      updateCurrentSpeed(finalSpeed);
    } else {
      console.log('No upload speeds recorded!');
    }
  };

  const renderSpeedGauge = () => {
    const maxSpeed = 500;

    // 根据测试阶段显示不同的速度，确保实时更新
    let speed = 0;
    if (testPhase === 'download') {
      speed = currentSpeed;
    } else if (testPhase === 'upload') {
      speed = currentSpeed;
    } else if (testPhase === 'complete') {
      // 完成阶段显示下载和上传速度的最大值
      speed = Math.max(result.downloadSpeed, result.uploadSpeed);
    }

    // 计算速度进度百分比（用于圆环显示）
    const speedProgress = Math.min(speed / maxSpeed, 1);

    // 生成动态渐变色，增加更多颜色层次
    const getSpeedColor = (speed: number) => {
      if (speed < 5) return '#10b981'; // 绿色
      if (speed < 25) return '#06b6d4'; // 青色
      if (speed < 50) return '#3b82f6'; // 蓝色
      if (speed < 100) return '#8b5cf6'; // 紫色
      if (speed < 200) return '#f59e0b'; // 橙色
      return '#ef4444'; // 红色
    };

    const speedColor = getSpeedColor(speed);

    // 添加动画关键帧，确保平滑过渡
    const animationDuration = testPhase === 'download' || testPhase === 'upload' ? '0.3s' : '1s';

    return (
      <div className="flex flex-col items-center justify-center">
        <div className="relative w-96 h-96 speed-gauge-container">
          {/* 外层装饰环 */}
          <div className={`absolute inset-0 rounded-full border-4 ${
            isDarkMode ? 'border-slate-700' : 'border-gray-200'
          }`}></div>

          {/* 主要测速环 */}
          <div className="absolute inset-4">
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 200 200">
              {/* 背景圆环 */}
              <circle
                cx="100"
                cy="100"
                r="85"
                fill="none"
                stroke={isDarkMode ? '#374151' : '#e5e7eb'}
                strokeWidth="12"
                strokeLinecap="round"
              />

              {/* 进度圆环 */}
              <circle
                cx="100"
                cy="100"
                r="85"
                fill="none"
                stroke={speedColor}
                strokeWidth="12"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 85}`}
                strokeDashoffset={`${2 * Math.PI * 85 * (1 - speedProgress)}`}
                className="speed-circle speed-animation"
                style={{
                  filter: `drop-shadow(0 0 8px ${speedColor}40)`,
                  transition: `all ${animationDuration} ease-out`
                }}
              />

              {/* 内层装饰圆环 */}
              <circle
                cx="100"
                cy="100"
                r="65"
                fill="none"
                stroke={isDarkMode ? '#4b5563' : '#f3f4f6'}
                strokeWidth="2"
                opacity="0.5"
              />
            </svg>

            {/* 速度刻度 */}
            <div className="absolute inset-0">
              {[0, 50, 100, 150, 200, 300, 500, 0].map((value, index) => {
                const angle = (index * 360) / 7 - 90; // 从顶部开始分布，8个刻度点
                const radian = (angle * Math.PI) / 180;
                const radius = 110;
                const x = 50 + (radius / 2) * Math.cos(radian);
                const y = 50 + (radius / 2) * Math.sin(radian);

                return (
                  <div
                    key={`${value}-${index}`} // 使用index避免重复key
                    className={`absolute text-sm font-medium ${
                      isDarkMode ? 'text-slate-400' : 'text-gray-600'
                    }`}
                    style={{
                      left: `${x}%`,
                      top: `${y}%`,
                      transform: 'translate(-50%, -50%)'
                    }}
                  >
                    {value}
                  </div>
                );
              })}
            </div>
          </div>

          {/* 中心显示区域 */}
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <div className="text-center">
              {/* 主要速度显示 */}
              <div
                className="text-7xl font-bold leading-none speed-text speed-animation"
                style={{
                  color: speedColor,
                  transition: `color ${animationDuration} ease-out`
                }}
              >
                {Math.floor(speed)}
                <span className="text-4xl">
                  .{String(Math.round((speed % 1) * 100)).padStart(2, '0')}
                </span>
              </div>

              {/* 单位 */}
              <div
                className="text-2xl mt-2 font-medium speed-text speed-animation"
                style={{
                  color: speedColor,
                  transition: `color ${animationDuration} ease-out`
                }}
              >
                Mbps
              </div>

              {/* 状态指示器 */}
              <div className={`text-sm mt-4 ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                {testPhase === 'idle' && '准备测速'}
                {testPhase === 'latency' && '延迟测试中...'}
                {testPhase === 'download' && '下载测试中...'}
                {testPhase === 'upload' && '上传测试中...'}
                {testPhase === 'complete' && '测试完成'}
                {testPhase === 'error' && (
                  <span className={isDarkMode ? 'text-red-400' : 'text-red-500'}>
                    测试失败: {errorMessage}
                  </span>
                )}
              </div>

              {/* 测试进度百分比 */}
              {(testPhase === 'download' || testPhase === 'upload') && (
                <div className={`text-xs mt-2 ${isDarkMode ? 'text-slate-500' : 'text-gray-400'}`}>
                  {Math.round(progress)}%
                </div>
              )}
            </div>
          </div>

          {/* 装饰性光效 */}
          {speed > 0 && (
            <div
              className="absolute inset-8 rounded-full opacity-20 animate-pulse"
              style={{
                background: `radial-gradient(circle, ${speedColor}20 0%, transparent 70%)`
              }}
            ></div>
          )}
        </div>

        {/* 取消按钮 */}
        {(testPhase !== 'idle' && testPhase !== 'complete') && (
          <button
            onClick={startTest}
            className={`mt-8 px-10 py-4 border-2 rounded-full transition-all duration-300 font-medium text-lg ${
              isDarkMode
                ? 'border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500'
                : 'border-gray-300 text-gray-600 hover:bg-gray-50 hover:border-gray-400'
            } hover:scale-105`}
          >
            取消测速
          </button>
        )}
      </div>
    );
  };

  // 渲染GO测速图标（开始页面）
  const renderGoButton = () => {
    return (
      <div className="flex flex-col items-center justify-center">
        <div className="relative">
          {/* GO测速圆形按钮 */}
          <button
            onClick={startTest}
            disabled={!isIpReady()}
            className={`w-64 h-64 rounded-full text-white text-3xl font-bold transition-all duration-300 shadow-2xl ${
              !isIpReady()
                ? // 禁用状态
                  isDarkMode
                    ? 'bg-gradient-to-br from-slate-600 to-slate-700 cursor-not-allowed opacity-60'
                    : 'bg-gradient-to-br from-gray-400 to-gray-500 cursor-not-allowed opacity-60'
                : // 正常状态
                  isDarkMode
                    ? 'bg-gradient-to-br from-cyan-400 to-teal-500 hover:from-cyan-300 hover:to-teal-400 hover:scale-105 hover:shadow-3xl'
                    : 'bg-gradient-to-br from-cyan-500 to-teal-600 hover:from-cyan-400 hover:to-teal-500 hover:scale-105 hover:shadow-3xl'
            }`}
          >
            {!isIpReady() ? '等待中...' : '测速'}
          </button>
        </div>

        {/* 服务器状态 */}
        <div className="mt-6">
          {serverSelectionStatus === 'selecting' && (
            <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
              正在选择最佳服务器...
            </div>
          )}
          {serverSelectionStatus === 'selected' && selectedServer && (
            <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
              测试服务器: {selectedServer.name} ({selectedServer.location})
            </div>
          )}
          {serverSelectionStatus === 'failed' && (
            <div className={`text-sm ${isDarkMode ? 'text-red-400' : 'text-red-500'}`}>
              服务器选择失败，将使用本地服务器
            </div>
          )}

          {/* 网络连接状态指示器 */}
          {!isOnline && (
            <div className={`text-sm ${isDarkMode ? 'text-red-400' : 'text-red-500'}`}>
              ⚠️ 网络连接已断开
            </div>
          )}
        </div>

        {/* IP信息 */}
        <div className="flex justify-center items-center mt-4 space-x-8">
          <div className={`flex items-center ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
            <MapPin className="w-4 h-4 mr-2" />
            <span>{result.ip}</span>
          </div>
          <div className={`flex items-center ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
            <Globe className="w-4 h-4 mr-2" />
            <span>{result.location}</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`min-h-screen flex flex-col transition-colors duration-300 ${
      isDarkMode
        ? 'bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900'
        : 'bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-100'
    }`}>
      {/* 顶部提示框 */}
      {showNotification && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 animate-in slide-in-from-top-2 duration-300">
          <div className={`px-6 py-4 rounded-lg shadow-lg border backdrop-blur-sm ${
            isDarkMode
              ? 'bg-slate-800/90 border-slate-600 text-white'
              : 'bg-white/90 border-gray-200 text-gray-800'
          }`}>
            <div className="flex items-center space-x-3">
              <div className={`w-2 h-2 rounded-full ${
                isDarkMode ? 'bg-yellow-400' : 'bg-yellow-500'
              } animate-pulse`}></div>
              <span className="font-medium">
                请等待IP地址获取完成后再开始测速
              </span>
            </div>
          </div>
        </div>
      )}
      <div className="flex-1 flex flex-col justify-center items-center px-4">
        <div className="fixed top-4 right-4 z-50">
          <button
            onClick={toggleDarkMode}
            className={`p-3 rounded-full transition-all duration-300 ${
              isDarkMode
                ? 'bg-slate-700/80 hover:bg-slate-600/80 text-yellow-400 backdrop-blur-sm'
                : 'bg-white/80 hover:bg-gray-50/80 text-gray-600 shadow-lg backdrop-blur-sm'
            }`}
          >
            {isDarkMode ? <Sun className="w-6 h-6" /> : <Moon className="w-6 h-6" />}
          </button>
        </div>

        <div className="text-center mb-12">
          <h1 className="text-6xl font-black mb-2 gradient-animate" style={{ fontFamily: 'KaiTi, STKaiti, "华文楷体", serif', fontWeight: 'bold' }}>
            𝓌𝑜𝒷测速
          </h1>
        </div>

        <div className={`rounded-2xl shadow-2xl p-8 transition-colors duration-300 max-w-2xl w-full ${
          isDarkMode
            ? 'bg-slate-800/80 backdrop-blur-sm border border-slate-700'
            : 'bg-white/90 backdrop-blur-sm border border-white/20'
        }`}>
          {testPhase === 'idle' ? (
            <div className="text-center">
              {renderGoButton()}
            </div>
          ) : (testPhase === 'latency' || testPhase === 'download' || testPhase === 'upload') ? (
            <div className="text-center">
              {renderSpeedGauge()}

              <div className="mt-6 mb-6">
                <div className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                  {testPhase === 'latency' && '正在测试延迟...'}
                  {testPhase === 'download' && '正在测试下载速度...'}
                  {testPhase === 'upload' && '正在测试上传速度...'}
                </div>

                {/* 延迟测试进度条 */}
                {testPhase === 'latency' && (
                  <div className="mb-4">
                    <div className={`w-64 mx-auto rounded-full h-2 ${isDarkMode ? 'bg-slate-700' : 'bg-gray-200'}`}>
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          isDarkMode
                            ? 'bg-gradient-to-r from-cyan-500 to-teal-500'
                            : 'bg-gradient-to-r from-cyan-400 to-teal-400'
                        }`}
                        style={{ width: `${latencyProgress}%` }}
                      />
                    </div>
                    <div className={`text-xs mt-1 ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                      {Math.round(latencyProgress)}%
                    </div>
                  </div>
                )}

                {/* 下载/上传测试进度条 */}
                {(testPhase === 'download' || testPhase === 'upload') && (
                  <div className={`w-64 mx-auto rounded-full h-2 ${isDarkMode ? 'bg-slate-700' : 'bg-gray-200'}`}>
                    <div
                      className={`h-2 rounded-full speed-progress-bar speed-animation ${
                        isDarkMode
                          ? 'bg-gradient-to-r from-blue-500 to-purple-500'
                          : 'bg-gradient-to-r from-teal-500 to-blue-500'
                      }`}
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                )}
              </div>

              {testPhase !== 'latency' && (
                <div className="grid grid-cols-2 gap-8 mb-6">
                  <div className="text-center">
                    <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>下载/Mbps</div>
                    <div className={`text-xl font-bold transition-all duration-300 ease-out ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                      {testPhase === 'download' ? Math.round(currentSpeed * 10) / 10 : result.downloadSpeed}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>上传/Mbps</div>
                    <div className={`text-xl font-bold transition-all duration-300 ease-out ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                      {testPhase === 'upload' ? Math.round(currentSpeed * 10) / 10 : result.uploadSpeed}
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-8 mb-6">
                <div className="text-center">
                  <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>时延/ms</div>
                  <div className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{result.latency}</div>
                </div>
                <div className="text-center">
                  <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>抖动/ms</div>
                  <div className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{result.jitter}</div>
                </div>
              </div>

              <div className="flex justify-center items-center mt-6 space-x-8">
                <div className={`flex items-center ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                  <MapPin className="w-4 h-4 mr-2" />
                  <span>{result.ip}</span>
                </div>
                <div className={`flex items-center ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                  <Globe className="w-4 h-4 mr-2" />
                  <span>{result.location}</span>
                </div>
              </div>
            </div>
          ) : testPhase === 'error' ? (
            <div className="text-center">
              {/* 错误状态显示 */}
              <div className={`p-6 rounded-xl ${isDarkMode ? 'bg-red-900/20 border border-red-800' : 'bg-red-50 border border-red-200'}`}>
                <div className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-red-400' : 'text-red-600'}`}>
                  测试失败
                </div>
                <div className={`text-sm mb-4 ${isDarkMode ? 'text-red-300' : 'text-red-500'}`}>
                  {errorMessage}
                </div>

                {/* 重试按钮 */}
                <div className="flex justify-center space-x-4">
                  <button
                    onClick={retryTest}
                    className={`px-6 py-2 rounded-lg font-medium transition-all duration-300 ${
                      isDarkMode
                        ? 'bg-red-600 hover:bg-red-700 text-white'
                        : 'bg-red-500 hover:bg-red-600 text-white'
                    }`}
                  >
                    重试测试 {retryCount > 0 && `(${retryCount})`}
                  </button>

                  <button
                    onClick={() => {
                      setTestPhase('idle');
                      setErrorMessage('');
                      setRetryCount(0);
                    }}
                    className={`px-6 py-2 rounded-lg font-medium transition-all duration-300 ${
                      isDarkMode
                        ? 'bg-slate-600 hover:bg-slate-700 text-white'
                        : 'bg-gray-500 hover:bg-gray-600 text-white'
                    }`}
                  >
                    返回首页
                  </button>
                </div>
              </div>
            </div>
          ) : testPhase === 'complete' ? (
            <div className="text-center">
              {renderSpeedGauge()}

              {/* 测试结果详情 */}
              <div className="mt-8 grid grid-cols-2 gap-6">
                <div className={`p-4 rounded-xl ${isDarkMode ? 'bg-slate-700/50' : 'bg-gray-50'}`}>
                  <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>下载速度</div>
                  <div className={`text-2xl font-bold mt-1 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                    {result.downloadSpeed} Mbps
                  </div>
                  <div className={`text-xs mt-1 ${isDarkMode ? 'text-slate-500' : 'text-gray-400'}`}>
                    数据: {result.downloadData} MB
                  </div>
                </div>

                <div className={`p-4 rounded-xl ${isDarkMode ? 'bg-slate-700/50' : 'bg-gray-50'}`}>
                  <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>上传速度</div>
                  <div className={`text-2xl font-bold mt-1 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                    {result.uploadSpeed} Mbps
                  </div>
                  <div className={`text-xs mt-1 ${isDarkMode ? 'text-slate-500' : 'text-gray-400'}`}>
                    数据: {result.uploadData} MB
                  </div>
                </div>

                <div className={`p-4 rounded-xl ${isDarkMode ? 'bg-slate-700/50' : 'bg-gray-50'}`}>
                  <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>延迟</div>
                  <div className={`text-2xl font-bold mt-1 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                    {result.latency} ms
                  </div>
                </div>

                <div className={`p-4 rounded-xl ${isDarkMode ? 'bg-slate-700/50' : 'bg-gray-50'}`}>
                  <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>抖动</div>
                  <div className={`text-2xl font-bold mt-1 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                    {result.jitter} ms
                  </div>
                </div>
              </div>

              {/* 重新测试按钮 */}
              <button
                onClick={startTest}
                className={`mt-8 w-48 h-12 rounded-full text-white text-lg font-medium transition-all duration-300 shadow-lg ${
                  isDarkMode
                    ? 'bg-gradient-to-r from-blue-600 to-purple-700 hover:from-blue-500 hover:to-purple-600'
                    : 'bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-400 hover:to-blue-500'
                } hover:scale-105 hover:shadow-xl`}
              >
                重新测速
              </button>

              {/* IP信息 */}
              <div className="flex justify-center items-center mt-6 space-x-8">
                <div className={`flex items-center ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                  <MapPin className="w-4 h-4 mr-2" />
                  <span>{result.ip}</span>
                </div>
                <div className={`flex items-center ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                  <Globe className="w-4 h-4 mr-2" />
                  <span>{result.location}</span>
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </div>

      <footer className={`mt-auto py-4 text-center text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`} style={{ fontFamily: 'KaiTi, STKaiti, "华文楷体", serif', fontWeight: 'bold' }}>
        {/* IP和ISP信息 */}
        <div className="flex justify-center items-center space-x-6">
          <div className="flex items-center">
            <MapPin className="w-4 h-4 mr-1" />
            <span>{result.ip}</span>
          </div>
          <div className="flex items-center">
            <Globe className="w-4 h-4 mr-1" />
            <span>{result.location} {result.isp}</span>
          </div>
        </div>

        {/* 性能信息显示（始终显示，提供详细统计） */}
        {performanceInfo.updateCount > 0 && (
          <div className={`mt-2 text-xs ${isDarkMode ? 'text-slate-500' : 'text-gray-400'}`}>
            性能监控: 更新次数 {performanceInfo.updateCount} | 平均间隔 {Math.round(performanceInfo.avgUpdateInterval)}ms
            {performanceInfo.minInterval !== Infinity && (
              <span> | 间隔范围 {Math.round(performanceInfo.minInterval)}-{Math.round(performanceInfo.maxInterval)}ms</span>
            )}
          </div>
        )}

        {/* 调试信息显示（始终显示，提供详细测量统计） */}
        <div className={`mt-1 text-xs ${isDarkMode ? 'text-slate-500' : 'text-gray-400'}`}>
          下载测量: {debugInfo.downloadMeasurements}次 ({debugInfo.lastDownloadSpeed.toFixed(1)}Mbps) |
          上传测量: {debugInfo.uploadMeasurements}次 ({debugInfo.lastUploadSpeed.toFixed(1)}Mbps)
          {debugInfo.testStartTime > 0 && testPhase === 'complete' && (
            <span> | 总耗时 {Math.round((Date.now() - debugInfo.testStartTime) / 1000)}秒</span>
          )}
          {testPhase !== 'idle' && testPhase !== 'complete' && (
            <span> | 测试进行中...</span>
          )}
        </div>

        {/* 版权信息移到调试信息下面 */}
        <div className="mt-2">
          <p>© 2023-2025 𝓌𝑜𝒷测速 | Powered by <a href="https://wobshare.us.kg/" target="_blank" rel="noopener noreferrer" className={`transition-colors ${isDarkMode ? 'text-blue-400 hover:text-blue-300' : 'text-teal-500 hover:text-teal-600'}`}>𝓌𝑜𝒷</a></p>
        </div>
      </footer>
    </div>
  );
}
