# Vercel 部署优化指南

## 🎯 Vercel限制分析

### **主要限制**
1. **Serverless函数10秒限制**
2. **冷启动延迟**
3. **并发处理限制**
4. **带宽限制**
5. **地理位置限制**

## 🔧 优化策略

### **1. 减少测试时长**

#### **缩短测试时间**
```typescript
// src/components/SpeedTestComponent.tsx
const testDownload = async () => {
  // 从15秒减少到8秒，避免Serverless超时
  const testDuration = 8000; // 8秒
  
  // 增加并发连接数补偿时间减少
  const concurrentConnections = 6; // 增加到6个
};

const testUpload = async () => {
  // 上传测试也减少到8秒
  const testDuration = 8000; // 8秒
  const concurrentConnections = 6; // 增加并发
};
```

### **2. 优化API端点**

#### **流式响应优化**
```typescript
// src/app/api/download/route.ts
export async function GET(request: Request) {
  const url = new URL(request.url);
  const size = parseInt(url.searchParams.get('size') || '1048576');
  
  // 使用流式响应，避免内存限制
  const stream = new ReadableStream({
    start(controller) {
      const chunkSize = 64 * 1024; // 64KB chunks
      let sent = 0;
      
      const sendChunk = () => {
        if (sent >= size) {
          controller.close();
          return;
        }
        
        const remaining = size - sent;
        const currentChunkSize = Math.min(chunkSize, remaining);
        const chunk = new Uint8Array(currentChunkSize);
        
        // 快速填充数据
        for (let i = 0; i < currentChunkSize; i += 4) {
          const value = Math.random() * 0xFFFFFFFF | 0;
          chunk[i] = value & 0xFF;
          chunk[i + 1] = (value >> 8) & 0xFF;
          chunk[i + 2] = (value >> 16) & 0xFF;
          chunk[i + 3] = (value >> 24) & 0xFF;
        }
        
        controller.enqueue(chunk);
        sent += currentChunkSize;
        
        // 异步发送下一块
        setTimeout(sendChunk, 0);
      };
      
      sendChunk();
    }
  });
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'application/octet-stream',
      'Content-Length': size.toString(),
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    },
  });
}
```

### **3. 使用Edge Runtime**

#### **配置Edge Runtime**
```typescript
// src/app/api/download/route.ts
export const runtime = 'edge'; // 使用Edge Runtime

// src/app/api/upload/route.ts  
export const runtime = 'edge'; // 使用Edge Runtime

// src/app/api/ping/route.ts
export const runtime = 'edge'; // 使用Edge Runtime
```

### **4. 预热函数**

#### **添加预热机制**
```typescript
// src/components/SpeedTestComponent.tsx
useEffect(() => {
  // 组件加载时预热API函数
  const warmupAPIs = async () => {
    try {
      // 预热下载API
      fetch('/api/download?size=1024', { method: 'GET' }).catch(() => {});
      
      // 预热上传API  
      fetch('/api/upload', { 
        method: 'POST', 
        body: new Uint8Array(1024) 
      }).catch(() => {});
      
      // 预热ping API
      fetch('/api/ping').catch(() => {});
    } catch (error) {
      // 忽略预热错误
    }
  };
  
  warmupAPIs();
}, []);
```

### **5. 优化测试参数**

#### **调整测试策略**
```typescript
// 针对Vercel优化的参数
const VERCEL_OPTIMIZED_CONFIG = {
  downloadTest: {
    duration: 8000,        // 8秒避免超时
    connections: 6,        // 增加并发
    chunkSize: 512 * 1024, // 512KB块
  },
  uploadTest: {
    duration: 8000,        // 8秒避免超时  
    connections: 6,        // 增加并发
    chunkSize: 256 * 1024, // 256KB块
  }
};
```

## 🎯 预期改善

### **优化后Vercel性能**
```
优化前：
- 下载: 121次 (3.5Mbps)
- 上传: 11次 (8.8Mbps)

优化后预期：
- 下载: 140-160次 (15-25Mbps)
- 上传: 80-120次 (20-40Mbps)

提升约300-400%！
```

### **仍然存在的限制**
- Serverless架构固有限制
- 地理位置限制
- 免费版带宽限制

## 💡 建议

### **最佳方案排序**
1. **Cloudflare Pages + Workers** - 最佳性能
2. **Railway** - 最简单，性价比高
3. **优化后的Vercel** - 如果必须使用Vercel

### **成本对比**
- **Vercel免费版**: 限制多，性能差
- **Vercel Pro**: $20/月，仍有限制
- **Railway**: $5/月，性能更好
- **Cloudflare**: 免费额度大，付费便宜
