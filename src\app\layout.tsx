import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "𝓌𝑜𝒷测速 - 专业的网络速度测试工具",
  description: "免费的网络速度测试工具，准确测量您的下载速度、上传速度和网络延迟。支持移动端，界面简洁美观。",
  keywords: "网速测试,测速,网络速度,下载速度,上传速度,延迟测试,网络测试,wob测速",
  authors: [{ name: "𝓌𝑜𝒷" }],
  robots: "index, follow",
  openGraph: {
    title: "𝓌𝑜𝒷测速 - 专业的网络速度测试工具",
    description: "免费的网络速度测试工具，准确测量您的下载速度、上传速度和网络延迟",
    type: "website",
    locale: "zh_CN",
  },
  twitter: {
    card: "summary_large_image",
    title: "𝓌𝑜𝒷测速 - 专业的网络速度测试工具",
    description: "免费的网络速度测试工具，准确测量您的下载速度、上传速度和网络延迟",
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <meta name="theme-color" content="#10b981" />
      </head>
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
