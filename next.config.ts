/** @type {import('next').NextConfig} */
const nextConfig = {
  // Cloudflare Pages 配置 - 启用静态导出
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // 图片优化
  images: {
    unoptimized: true,
  },

  // 禁用可能生成大文件的功能
  experimental: {
    webpackBuildWorker: false,
  },

  // 环境变量
  env: {
    CLOUDFLARE_OPTIMIZED: 'true'
  },

  // 自定义 webpack 配置，处理 API 路由问题
  webpack: (config: any, { isServer, dev }: { isServer: boolean, dev: boolean }) => {
    if (!dev && isServer) {
      // 在生产构建时，忽略 API 路由相关的错误
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  }
};

module.exports = nextConfig;
