// Cloudflare Pages Function for download test
export async function onRequest(context: any) {
  const { request } = context;
  
  // 处理 CORS 预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  }

  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    const url = new URL(request.url);
    const sizeParam = url.searchParams.get('size');
    const size = sizeParam ? parseInt(sizeParam, 10) : 1024 * 1024; // 默认1MB

    // 限制最大大小为100MB
    const maxSize = 100 * 1024 * 1024;
    const actualSize = Math.min(size, maxSize);

    // 创建流式响应
    const stream = new ReadableStream({
      start(controller) {
        const chunkSize = 64 * 1024; // 64KB chunks
        let sent = 0;

        const sendChunk = () => {
          if (sent >= actualSize) {
            controller.close();
            return;
          }

          const remainingBytes = actualSize - sent;
          const currentChunkSize = Math.min(chunkSize, remainingBytes);
          
          // 生成随机数据
          const chunk = new Uint8Array(currentChunkSize);
          for (let i = 0; i < currentChunkSize; i++) {
            chunk[i] = Math.floor(Math.random() * 256);
          }

          controller.enqueue(chunk);
          sent += currentChunkSize;

          // 异步发送下一个chunk
          setTimeout(sendChunk, 0);
        };

        sendChunk();
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'application/octet-stream',
        'Content-Length': actualSize.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('Download test error:', error);
    return new Response('Internal server error', { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
      }
    });
  }
}
