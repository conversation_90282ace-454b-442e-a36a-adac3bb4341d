// 真实测速服务器配置
export interface SpeedTestServer {
  id: string;
  name: string;
  location: string;
  country: string;
  downloadUrl: string;
  uploadUrl: string;
  pingUrl: string;
  priority: number; // 优先级，数字越小优先级越高
}

// 多个地理位置的测速服务器
export const SPEEDTEST_SERVERS: SpeedTestServer[] = [
  // Cloudflare全球CDN - 优先使用，性能最佳
  {
    id: 'cloudflare-global',
    name: 'Cloudflare CDN',
    location: '全球',
    country: '全球',
    downloadUrl: 'https://speed.cloudflare.com/__down?bytes=',
    uploadUrl: 'https://speed.cloudflare.com/__up',
    pingUrl: 'https://speed.cloudflare.com/__down?bytes=1',
    priority: 1
  },

  // 国内服务器 - 作为备用
  {
    id: 'local-api',
    name: '本地API',
    location: '本地',
    country: '中国',
    downloadUrl: '/api/download?size=',
    uploadUrl: '/api/upload',
    pingUrl: '/api/ping',
    priority: 2
  },
  // 专业测速服务 - 高性能
  {
    id: 'speedtest-net',
    name: 'Speedtest.net',
    location: '全球',
    country: '全球',
    downloadUrl: 'https://www.speedtest.net/api/js/servers?engine=js&limit=10',
    uploadUrl: 'https://www.speedtest.net/api/js/upload',
    pingUrl: 'https://www.speedtest.net/api/js/ping',
    priority: 3
  },

  // 高性能CDN服务
  {
    id: 'jsdelivr-cdn',
    name: 'jsDelivr CDN',
    location: '全球',
    country: '全球',
    downloadUrl: 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js',
    uploadUrl: 'https://httpbin.org/post',
    pingUrl: 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/package.json',
    priority: 4
  },

  {
    id: 'baidu-cn',
    name: '百度',
    location: '北京',
    country: '中国',
    downloadUrl: 'https://www.baidu.com',
    uploadUrl: '/api/upload',
    pingUrl: 'https://www.baidu.com/favicon.ico',
    priority: 5
  },
  
  // 其他公共测速服务
  {
    id: 'fast-com',
    name: 'Fast.com (Netflix)',
    location: '全球',
    country: '全球',
    downloadUrl: 'https://api.fast.com/netflix/speedtest/v2/download',
    uploadUrl: 'https://api.fast.com/netflix/speedtest/v2/upload',
    pingUrl: 'https://api.fast.com/netflix/speedtest/v2/ping',
    priority: 4
  },
  
  // 备用服务器 - 使用大文件下载测试
  {
    id: 'github-releases',
    name: 'GitHub Releases',
    location: '全球',
    country: '全球',
    downloadUrl: 'https://github.com/microsoft/vscode/releases/download/1.85.0/VSCode-win32-x64-1.85.0.zip',
    uploadUrl: 'https://httpbin.org/post',
    pingUrl: 'https://api.github.com',
    priority: 5
  },
  
  // 更多备用服务器
  {
    id: 'httpbin',
    name: 'HTTPBin测试',
    location: '全球',
    country: '全球',
    downloadUrl: 'https://httpbin.org/bytes/',
    uploadUrl: 'https://httpbin.org/post',
    pingUrl: 'https://httpbin.org/get',
    priority: 6
  }
];

// 测试文件大小配置（字节）
export const TEST_SIZES = {
  PING: 1,
  SMALL: 1024 * 100,      // 100KB
  MEDIUM: 1024 * 1024,    // 1MB  
  LARGE: 1024 * 1024 * 10, // 10MB
  XLARGE: 1024 * 1024 * 25 // 25MB
};

// 测试配置
export const TEST_CONFIG = {
  LATENCY_TESTS: 10,        // 延迟测试次数
  DOWNLOAD_DURATION: 15000, // 下载测试时长(ms)
  UPLOAD_DURATION: 15000,   // 上传测试时长(ms)
  CONCURRENT_CONNECTIONS: 4, // 并发连接数
  WARMUP_DURATION: 2000,    // 预热时间(ms)
  MIN_TEST_DURATION: 5000,  // 最小测试时间(ms)
  MAX_TEST_DURATION: 30000, // 最大测试时间(ms)
};

// 智能服务器选择算法（基于地理位置）
export async function selectBestServer(userLocation: string = ''): Promise<SpeedTestServer> {

  // 检测用户是否在中国 - 全面的地理位置识别
  const isChineseUser = userLocation.includes('中国') ||
                       userLocation.includes('China') ||
                       userLocation.includes('CN') ||
                       // 主要城市（中文）
                       userLocation.includes('北京') ||
                       userLocation.includes('上海') ||
                       userLocation.includes('广州') ||
                       userLocation.includes('深圳') ||
                       userLocation.includes('杭州') ||
                       userLocation.includes('南京') ||
                       userLocation.includes('武汉') ||
                       userLocation.includes('成都') ||
                       userLocation.includes('重庆') ||
                       userLocation.includes('天津') ||
                       userLocation.includes('西安') ||
                       userLocation.includes('苏州') ||
                       userLocation.includes('青岛') ||
                       userLocation.includes('大连') ||
                       userLocation.includes('厦门') ||
                       userLocation.includes('宁波') ||
                       userLocation.includes('无锡') ||
                       userLocation.includes('长沙') ||
                       userLocation.includes('郑州') ||
                       userLocation.includes('沈阳') ||
                       userLocation.includes('哈尔滨') ||
                       userLocation.includes('长春') ||
                       userLocation.includes('石家庄') ||
                       userLocation.includes('太原') ||
                       userLocation.includes('济南') ||
                       userLocation.includes('合肥') ||
                       userLocation.includes('福州') ||
                       userLocation.includes('南昌') ||
                       userLocation.includes('昆明') ||
                       userLocation.includes('贵阳') ||
                       userLocation.includes('兰州') ||
                       userLocation.includes('银川') ||
                       userLocation.includes('西宁') ||
                       userLocation.includes('乌鲁木齐') ||
                       userLocation.includes('拉萨') ||
                       userLocation.includes('呼和浩特') ||
                       userLocation.includes('南宁') ||
                       userLocation.includes('海口') ||
                       userLocation.includes('三亚') ||
                       // 主要城市（英文）
                       userLocation.includes('Beijing') ||
                       userLocation.includes('Shanghai') ||
                       userLocation.includes('Guangzhou') ||
                       userLocation.includes('Shenzhen') ||
                       userLocation.includes('Hangzhou') ||
                       userLocation.includes('Nanjing') ||
                       userLocation.includes('Wuhan') ||
                       userLocation.includes('Chengdu') ||
                       userLocation.includes('Chongqing') ||
                       userLocation.includes('Tianjin') ||
                       userLocation.includes('Xian') ||
                       userLocation.includes('Suzhou') ||
                       userLocation.includes('Qingdao') ||
                       userLocation.includes('Dalian') ||
                       userLocation.includes('Xiamen') ||
                       userLocation.includes('Ningbo') ||
                       userLocation.includes('Wuxi') ||
                       userLocation.includes('Changsha') ||
                       userLocation.includes('Zhengzhou') ||
                       userLocation.includes('Shenyang') ||
                       userLocation.includes('Harbin') ||
                       userLocation.includes('Changchun') ||
                       userLocation.includes('Shijiazhuang') ||
                       userLocation.includes('Taiyuan') ||
                       userLocation.includes('Jinan') ||
                       userLocation.includes('Hefei') ||
                       userLocation.includes('Fuzhou') ||
                       userLocation.includes('Nanchang') ||
                       userLocation.includes('Kunming') ||
                       userLocation.includes('Guiyang') ||
                       userLocation.includes('Lanzhou') ||
                       userLocation.includes('Yinchuan') ||
                       userLocation.includes('Xining') ||
                       userLocation.includes('Urumqi') ||
                       userLocation.includes('Lhasa') ||
                       userLocation.includes('Hohhot') ||
                       userLocation.includes('Nanning') ||
                       userLocation.includes('Haikou') ||
                       userLocation.includes('Sanya') ||
                       // 省份简称
                       userLocation.includes('BJ') ||  // 北京
                       userLocation.includes('SH') ||  // 上海
                       userLocation.includes('TJ') ||  // 天津
                       userLocation.includes('CQ') ||  // 重庆
                       userLocation.includes('GD') ||  // 广东
                       userLocation.includes('JS') ||  // 江苏
                       userLocation.includes('ZJ') ||  // 浙江
                       userLocation.includes('SD') ||  // 山东
                       userLocation.includes('HB') ||  // 河北/湖北
                       userLocation.includes('HN') ||  // 河南/湖南
                       userLocation.includes('SC') ||  // 四川
                       userLocation.includes('LN') ||  // 辽宁
                       userLocation.includes('JL') ||  // 吉林
                       userLocation.includes('HL') ||  // 黑龙江
                       userLocation.includes('SX') ||  // 山西/陕西
                       userLocation.includes('AH') ||  // 安徽
                       userLocation.includes('FJ') ||  // 福建
                       userLocation.includes('JX') ||  // 江西
                       userLocation.includes('YN') ||  // 云南
                       userLocation.includes('GZ') ||  // 贵州
                       userLocation.includes('GS') ||  // 甘肃
                       userLocation.includes('QH') ||  // 青海
                       userLocation.includes('NX') ||  // 宁夏
                       userLocation.includes('XJ') ||  // 新疆
                       userLocation.includes('XZ') ||  // 西藏
                       userLocation.includes('NM') ||  // 内蒙古
                       userLocation.includes('GX') ||  // 广西
                       userLocation.includes('HI') ||  // 海南
                       // 常见的中国地区标识
                       userLocation.includes('Mainland') ||
                       userLocation.includes('mainland') ||
                       userLocation.includes('中华') ||
                       userLocation.includes('华东') ||
                       userLocation.includes('华南') ||
                       userLocation.includes('华北') ||
                       userLocation.includes('华中') ||
                       userLocation.includes('西南') ||
                       userLocation.includes('西北') ||
                       userLocation.includes('东北');

  // 根据地理位置过滤服务器
  const candidateServers = isChineseUser
    ? SPEEDTEST_SERVERS.filter(server =>
        server.country === '中国' ||
        server.location.includes('杭州') ||
        server.location.includes('深圳') ||
        server.id.includes('aliyun') ||
        server.id.includes('tencent')
      )
    : SPEEDTEST_SERVERS.filter(server =>
        server.country === '全球' ||
        server.location === '全球' ||
        server.id.includes('cloudflare') ||
        server.id.includes('github')
      );

  // 如果没有合适的候选服务器，使用所有服务器
  const serversToTest = candidateServers.length > 0 ? candidateServers : SPEEDTEST_SERVERS;

  // 并发测试候选服务器的延迟
  const latencyPromises = serversToTest.map(async (server) => {
    try {
      const start = performance.now();
      const response = await fetch(server.pingUrl, {
        method: 'HEAD',
        cache: 'no-cache',
        signal: AbortSignal.timeout(3000) // 3秒超时，加快选择速度
      });

      if (response.ok) {
        const latency = performance.now() - start;
        // 为中国用户的中国服务器添加优先级加成
        const priorityBonus = isChineseUser && server.country === '中国' ? -50 : 0;
        return { server, latency: latency + priorityBonus };
      }
    } catch (error) {
      console.log(`Server ${server.name} failed:`, error);
    }
    return null;
  });

  const latencyResults = await Promise.all(latencyPromises);

  // 过滤有效结果并按延迟排序
  const validResults = latencyResults
    .filter((result): result is {server: SpeedTestServer, latency: number} => result !== null)
    .sort((a, b) => a.latency - b.latency);

  if (validResults.length === 0) {
    // 如果所有服务器都失败，根据地理位置返回默认服务器
    return isChineseUser ? SPEEDTEST_SERVERS[0] : SPEEDTEST_SERVERS[2]; // 阿里云 或 Cloudflare
  }

  // 返回延迟最低的服务器
  return validResults[0].server;
}

// 生成随机测试数据
export function generateTestData(size: number): ArrayBuffer {
  const buffer = new ArrayBuffer(size);
  const view = new Uint8Array(buffer);
  
  // 填充随机数据
  for (let i = 0; i < size; i++) {
    view[i] = Math.floor(Math.random() * 256);
  }
  
  return buffer;
}
