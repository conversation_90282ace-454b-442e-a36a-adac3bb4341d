// Cloudflare Pages Function for upload test
export async function onRequest(context: any) {
  const { request } = context;
  
  // 处理 CORS 预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  }

  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    // 读取上传的数据
    const body = await request.arrayBuffer();
    const uploadedBytes = body.byteLength;

    // 模拟处理时间（可选）
    // await new Promise(resolve => setTimeout(resolve, 1));

    return new Response(JSON.stringify({
      success: true,
      bytesReceived: uploadedBytes,
      timestamp: Date.now()
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('Upload test error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Upload failed'
    }), { 
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      }
    });
  }
}
