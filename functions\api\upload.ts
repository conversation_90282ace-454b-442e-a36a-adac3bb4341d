// Cloudflare Pages Function for upload speed test
export async function onRequestPost(context: any) {
  try {
    // 读取上传的数据
    const body = await context.request.arrayBuffer();
    const uploadSize = body.byteLength;
    
    // 模拟处理时间（可选）
    await new Promise(resolve => setTimeout(resolve, 10));
    
    // 返回成功响应
    return new Response(JSON.stringify({
      success: true,
      bytesReceived: uploadSize,
      timestamp: Date.now()
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Cache-Control, Pragma',
      }
    });
    
  } catch (error) {
    console.error('Upload API error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      }
    );
  }
}

export async function onRequestOptions() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Cache-Control, Pragma',
      'Access-Control-Max-Age': '86400',
    },
  });
}
