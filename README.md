# 𝓌𝑜𝒷测速

一个现代化的网速测试应用，基于 Next.js 构建，提供准确的下载、上传速度测试和延迟测量。

## 功能特性

- 🚀 实时网速测试（下载/上传）
- 📊 延迟和抖动测量
- 📈 实时速度图表显示
- 🎯 保守准确的测速算法
- 📱 响应式设计，支持移动端
- ⚡ 基于 Next.js 15 和 React 19
- 🎨 使用 Tailwind CSS 美观界面
- 🌐 自动获取真实IP地址和位置信息
- 🌙 深色/浅色模式切换（默认深色）
- 🎨 动态彩色渐变标题动画
- 🚗 车速表样式仪表盘设计
- 📍 固定位置的主题切换按钮

## 技术栈

- **框架**: Next.js 15 (App Router)
- **UI**: React 19 + Tailwind CSS
- **图标**: Lucide React
- **语言**: TypeScript
- **部署**: Vercel

## 本地 开发

1. 克隆项目
```bash
git clone <repository-url>
cd speed
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run dev
```

4. 打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 构建部署

```bash
npm run build
npm start
```

## 部署

### Cloudflare Pages（推荐）

1. 将代码推送到 GitHub
2. 在 Cloudflare Pages 中连接仓库
3. 构建设置：
   - 构建命令: `npm run build`
   - 构建输出目录: `out`
   - Node.js 版本: `18`

### Vercel

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 自动部署完成

或使用 Vercel CLI：
```bash
npx vercel
```

## 测速算法说明

- **延迟测试**: 执行20次ping测试，去除异常值，计算平均延迟和抖动
- **下载测试**: 15秒测试时间，多线程下载，应用85%保守系数
- **上传测试**: 15秒测试时间，应用80%保守系数，确保结果准确性

## API 端点

- `GET /api/ping` - 延迟测试
- `GET /api/download` - 下载速度测试
- `POST /api/upload` - 上传速度测试

## 许可证

MIT License
