# Cloudflare Pages + Workers 部署指南

## 🎯 为什么选择Cloudflare

### **性能优势**
- **全球CDN**：200+数据中心，就近访问
- **更高带宽**：比Vercel更宽松的带宽限制
- **更长执行时间**：Workers支持更长的执行时间
- **更好的并发**：更强的并发处理能力

### **成本优势**
- **免费额度大**：每月100,000次请求免费
- **无冷启动**：Workers几乎无冷启动延迟
- **全球加速**：自动全球分发

## 🔧 部署步骤

### **1. 准备Cloudflare账户**
1. 注册 [Cloudflare](https://cloudflare.com) 账户
2. 安装Wrangler CLI：`npm install -g wrangler`
3. 登录：`wrangler login`

### **2. 修改项目结构**

#### **创建Workers API**
```bash
# 在项目根目录创建workers目录
mkdir workers
cd workers

# 初始化Worker项目
wrangler init speedtest-api
```

#### **Workers API代码**
```javascript
// workers/src/index.js
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // CORS headers
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    };

    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      if (path === '/api/download') {
        return handleDownload(request, corsHeaders);
      } else if (path === '/api/upload') {
        return handleUpload(request, corsHeaders);
      } else if (path === '/api/ping') {
        return handlePing(request, corsHeaders);
      }
      
      return new Response('Not Found', { status: 404 });
    } catch (error) {
      return new Response('Internal Server Error', { 
        status: 500,
        headers: corsHeaders 
      });
    }
  },
};

async function handleDownload(request, corsHeaders) {
  const url = new URL(request.url);
  const size = parseInt(url.searchParams.get('size')) || 1024 * 1024; // 1MB default
  
  // 生成随机数据
  const data = new Uint8Array(size);
  crypto.getRandomValues(data);
  
  return new Response(data, {
    headers: {
      ...corsHeaders,
      'Content-Type': 'application/octet-stream',
      'Content-Length': size.toString(),
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    },
  });
}

async function handleUpload(request, corsHeaders) {
  // 读取上传数据
  const data = await request.arrayBuffer();
  
  return new Response(JSON.stringify({
    success: true,
    size: data.byteLength,
    timestamp: Date.now()
  }), {
    headers: {
      ...corsHeaders,
      'Content-Type': 'application/json',
    },
  });
}

async function handlePing(request, corsHeaders) {
  return new Response(JSON.stringify({
    timestamp: Date.now(),
    server: 'cloudflare-worker'
  }), {
    headers: {
      ...corsHeaders,
      'Content-Type': 'application/json',
    },
  });
}
```

#### **Workers配置**
```toml
# workers/wrangler.toml
name = "speedtest-api"
main = "src/index.js"
compatibility_date = "2024-01-01"

[env.production]
routes = [
  { pattern = "your-domain.com/api/*", zone_name = "your-domain.com" }
]
```

### **3. 修改前端配置**

#### **更新API端点**
```typescript
// src/config/api-config.ts
export const API_CONFIG = {
  // Cloudflare Workers API
  baseUrl: process.env.NODE_ENV === 'production' 
    ? 'https://your-worker.your-subdomain.workers.dev'
    : 'http://localhost:3000',
  
  endpoints: {
    download: '/api/download',
    upload: '/api/upload',
    ping: '/api/ping'
  }
};
```

### **4. 部署流程**

#### **部署Workers API**
```bash
cd workers
wrangler deploy
```

#### **部署前端到Cloudflare Pages**
```bash
# 连接到Cloudflare Pages
npx wrangler pages project create speedtest-app

# 部署
npm run build
npx wrangler pages deploy dist --project-name speedtest-app
```

## 🎯 预期性能提升

### **Cloudflare vs Vercel**
| 指标 | Vercel | Cloudflare | 提升 |
|------|--------|------------|------|
| 执行时间限制 | 10秒 | 30秒+ | 200%+ |
| 冷启动延迟 | 100-500ms | <10ms | 90%+ |
| 全球CDN | 有限 | 200+节点 | 显著 |
| 带宽限制 | 严格 | 宽松 | 显著 |
| 并发处理 | 有限 | 更强 | 显著 |

### **预期测速结果**
```
Cloudflare部署预期：
- 下载: 150-200次 (50-100Mbps) 
- 上传: 120-180次 (80-150Mbps)
- 总耗时: 30-40秒

接近本地性能！
```
