import { NextRequest, NextResponse } from 'next/server';

// 配置为静态导出兼容
export const dynamic = 'force-static';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sizeParam = searchParams.get('size');
    
    // 默认大小为10MB，最大100MB
    const size = Math.min(parseInt(sizeParam || '10485760'), 100 * 1024 * 1024);
    
    // 设置响应头
    const headers = new Headers({
      'Content-Type': 'application/octet-stream',
      'Content-Length': size.toString(),
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Cache-Control, Pragma',
    });

    // 创建可读流
    const stream = new ReadableStream({
      start(controller) {
        const chunkSize = 64 * 1024; // 64KB chunks
        let bytesWritten = 0;
        
        const writeChunk = () => {
          if (bytesWritten >= size) {
            controller.close();
            return;
          }
          
          const remainingBytes = size - bytesWritten;
          const currentChunkSize = Math.min(chunkSize, remainingBytes);
          
          // 生成随机数据
          const chunk = new Uint8Array(currentChunkSize);
          for (let i = 0; i < currentChunkSize; i++) {
            chunk[i] = Math.floor(Math.random() * 256);
          }
          
          controller.enqueue(chunk);
          bytesWritten += currentChunkSize;
          
          // 异步继续写入下一个chunk
          setTimeout(writeChunk, 0);
        };
        
        writeChunk();
      }
    });

    return new NextResponse(stream, { headers });
    
  } catch (error) {
    console.error('Download API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Cache-Control, Pragma',
      'Access-Control-Max-Age': '86400',
    },
  });
}
