# 腾讯云Serverless配置文件
component: scf
name: ping-function-beijing
org: default
app: ping-test
stage: prod

inputs:
  name: ping-function-beijing
  src: ./
  handler: index.main_handler
  runtime: Nodejs18.15
  region: ap-beijing
  memorySize: 512
  timeout: 30
  environment:
    variables:
      CITY: 北京
      PROVINCE: 北京
      TENCENTCLOUD_REGION: ap-beijing
  events:
    - apigw:
        parameters:
          protocols:
            - https
          serviceName: ping-api-beijing
          environment: release
          endpoints:
            - path: /ping
              method: POST
              enableCORS: true
