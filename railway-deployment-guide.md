# Railway 部署指南

## 🎯 为什么选择Railway

### **优势**
- **真实服务器**：不是Serverless，无执行时间限制
- **更高性能**：持续运行的容器，无冷启动
- **简单部署**：直接从GitHub部署
- **合理价格**：$5/月起，性能远超Vercel免费版

### **性能对比**
| 特性 | Vercel免费版 | Railway | 优势 |
|------|-------------|---------|------|
| 执行时间 | 10秒限制 | 无限制 | ✅ |
| 冷启动 | 有 | 无 | ✅ |
| 内存 | 1GB | 8GB | ✅ |
| CPU | 共享 | 专用 | ✅ |
| 带宽 | 100GB/月 | 无限制 | ✅ |

## 🚀 部署步骤

### **1. 准备Railway账户**
1. 访问 [Railway.app](https://railway.app)
2. 使用GitHub账户登录
3. 连接你的GitHub仓库

### **2. 项目配置**

#### **添加Railway配置文件**
```json
// railway.json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "healthcheckPath": "/",
    "healthcheckTimeout": 100
  }
}
```

#### **更新package.json**
```json
{
  "scripts": {
    "build": "next build",
    "start": "next start -p $PORT",
    "dev": "next dev"
  },
  "engines": {
    "node": ">=18.0.0"
  }
}
```

### **3. 环境变量设置**
```bash
# Railway环境变量
NODE_ENV=production
PORT=3000
```

### **4. 部署流程**
1. 在Railway控制台点击"New Project"
2. 选择"Deploy from GitHub repo"
3. 选择你的speedtest项目
4. Railway自动检测Next.js项目并部署
5. 获得生产环境URL

### **5. 性能优化配置**

#### **Next.js配置优化**
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用压缩
  compress: true,
  
  // 优化图片
  images: {
    unoptimized: true
  },
  
  // API路由优化
  experimental: {
    serverComponentsExternalPackages: []
  },
  
  // 输出配置
  output: 'standalone'
}

module.exports = nextConfig
```

## 🎯 预期性能

### **Railway部署预期结果**
```
Railway部署预期：
- 下载: 160-200次 (80-120Mbps)
- 上传: 140-180次 (100-180Mbps) 
- 总耗时: 25-35秒

显著优于Vercel！
```

### **成本分析**
- **Railway Starter**: $5/月
- **8GB RAM + 8 vCPU**
- **无带宽限制**
- **24/7运行，无冷启动**

比Vercel Pro ($20/月) 更便宜，性能更好！
